import React, { useState } from 'react';
import { View, TextInput, TouchableOpacity, Text, StyleSheet, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../../App';
import colors from '../styles/colors';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Home'>;

export default function HomeScreen() {
  const [numLot, setNumLot] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const navigation = useNavigation<HomeScreenNavigationProp>();

  const validateLotNumber = (lotNumber: string): boolean => {
    const trimmed = lotNumber.trim();
    if (!trimmed) {
      Alert.alert('Validation Error', 'Please enter a lot number');
      return false;
    }
    if (trimmed.length < 2) {
      Alert.alert('Validation Error', 'Lot number must be at least 2 characters long');
      return false;
    }
    if (trimmed.length > 50) {
      Alert.alert('Validation Error', 'Lot number cannot exceed 50 characters');
      return false;
    }
    return true;
  };

  const handleSearch = async () => {
    if (!validateLotNumber(numLot)) {
      return;
    }

    setIsSearching(true);
    try {
      navigation.navigate('Results', { numLot: numLot.trim() });
    } finally {
      setIsSearching(false);
    }
  };

  const handleScan = () => {
    navigation.navigate('Scanner');
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Lot Number Search</Text>
        <Text style={styles.subtitle}>Enter a lot number or scan a barcode</Text>

        <View style={styles.inputContainer}>
          <TextInput
            style={styles.input}
            placeholder="Enter lot number"
            value={numLot}
            onChangeText={setNumLot}
            autoCapitalize="characters"
            autoCorrect={false}
          />
        </View>

        <TouchableOpacity
          style={[styles.searchButton, isSearching && styles.disabledButton]}
          onPress={handleSearch}
          disabled={isSearching}
        >
          <Text style={styles.buttonText}>
            {isSearching ? 'Searching...' : 'Search'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.scanButton} onPress={handleScan}>
          <Text style={styles.buttonText}>Scan Barcode</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 40,
    color: '#666',
  },
  inputContainer: {
    marginBottom: 20,
  },
  input: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 15,
    fontSize: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  searchButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
    elevation: 3,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3,
  },
  scanButton: {
    backgroundColor: colors.secondary,
    borderRadius: 8,
    padding: 15,
    elevation: 3,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3,
  },
  buttonText: {
    color: colors.white,
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  disabledButton: {
    backgroundColor: colors.gray,
    opacity: 0.6,
  },
});