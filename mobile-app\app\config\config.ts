// App Configuration
export const config = {
  // API Configuration
  api: {
    baseUrl: __DEV__ 
      ? 'http://localhost:8000/api'  // Development URL
      : 'https://your-production-api.com/api',  // Production URL
    timeout: 10000, // 10 seconds
    retryAttempts: 3,
    retryDelay: 1000, // 1 second
  },
  
  // App Settings
  app: {
    name: 'Lot Scanner',
    version: '1.0.0',
    enableLogging: __DEV__,
    enableMockApi: __DEV__, // Use mock API in development
  },
  
  // Scanner Settings
  scanner: {
    enableVibration: true,
    enableSound: true,
    autoFocus: true,
    supportedBarcodeTypes: [
      'qr',
      'code128',
      'code39',
      'code93',
      'ean13',
      'ean8',
      'upc_a',
      'upc_e',
      'datamatrix',
      'pdf417',
    ],
  },
  
  // Validation Rules
  validation: {
    lotNumber: {
      minLength: 2,
      maxLength: 50,
      allowedCharacters: /^[A-Za-z0-9\-_]+$/, // Alphanumeric, hyphens, underscores
    },
  },
  
  // UI Settings
  ui: {
    animationDuration: 300,
    toastDuration: 3000,
    loadingTimeout: 30000, // 30 seconds
  },
  
  // Cache Settings
  cache: {
    enableCaching: true,
    cacheTimeout: 300000, // 5 minutes
    maxCacheSize: 100, // Maximum number of cached items
  },
};

export default config;
