import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { RootStackParamList } from '../../App';
import api, { LotData } from '../services/api';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';
import colors from '../styles/colors';

type ResultsScreenRouteProp = RouteProp<RootStackParamList, 'Results'>;
type ResultsScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Results'>;

export default function ResultsScreen() {
  const route = useRoute<ResultsScreenRouteProp>();
  const navigation = useNavigation<ResultsScreenNavigationProp>();
  const { numLot, scanned } = route.params;
  
  const [lotData, setLotData] = useState<LotData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchLotData();
  }, [numLot]);

  const fetchLotData = async () => {
    try {
      setLoading(true);
      setError(null);

      const data = await api.searchLot(numLot);
      setLotData(data);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch lot data. Please try again.';
      setError(errorMessage);
      console.error('Error fetching lot data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRetry = () => {
    fetchLotData();
  };

  const handleNewSearch = () => {
    navigation.navigate('Home');
  };

  const handleScanAnother = () => {
    navigation.navigate('Scanner');
  };

  if (loading) {
    return <LoadingSpinner message="Loading lot information..." />;
  }

  if (error) {
    return (
      <View style={styles.centerContainer}>
        <ErrorMessage message={error} onRetry={handleRetry} />
        <TouchableOpacity style={styles.homeButton} onPress={handleNewSearch}>
          <Text style={styles.buttonText}>New Search</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!lotData) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.notFoundText}>❌ Lot number not found</Text>
        <Text style={styles.notFoundSubtext}>
          The lot number "{numLot}" was not found in the database.
        </Text>
        <TouchableOpacity style={styles.homeButton} onPress={handleNewSearch}>
          <Text style={styles.buttonText}>New Search</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>
          {scanned ? '📱 Scanned' : '🔍 Searched'} Lot Details
        </Text>
        <Text style={styles.lotNumber}>{lotData.num_lot}</Text>
      </View>

      <View style={styles.content}>
        <View style={styles.infoCard}>
          <Text style={styles.cardTitle}>Product Information</Text>
          <InfoRow label="Product Name" value={lotData.product_name} />
          <InfoRow label="Type" value={lotData.type} />
          <InfoRow label="Batch Size" value={lotData.batch_size?.toString()} />
          <InfoRow label="Status" value={lotData.status} />
        </View>

        <View style={styles.infoCard}>
          <Text style={styles.cardTitle}>Dates</Text>
          <InfoRow label="Production Date" value={lotData.production_date} />
          <InfoRow label="Expiry Date" value={lotData.expiry_date} />
        </View>

        <View style={styles.infoCard}>
          <Text style={styles.cardTitle}>Quality & Location</Text>
          <InfoRow label="Quality Check" value={lotData.quality_check} />
          <InfoRow label="Location" value={lotData.location} />
        </View>

        {lotData.notes && (
          <View style={styles.infoCard}>
            <Text style={styles.cardTitle}>Notes</Text>
            <Text style={styles.notesText}>{lotData.notes}</Text>
          </View>
        )}
      </View>

      <View style={styles.actionButtons}>
        <TouchableOpacity style={styles.scanButton} onPress={handleScanAnother}>
          <Text style={styles.buttonText}>Scan Another</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.searchButton} onPress={handleNewSearch}>
          <Text style={styles.buttonText}>New Search</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const InfoRow: React.FC<{ label: string; value?: string }> = ({ label, value }) => (
  <View style={styles.infoRow}>
    <Text style={styles.label}>{label}:</Text>
    <Text style={styles.value}>{value || 'N/A'}</Text>
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: colors.primary,
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
  },
  lotNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  content: {
    padding: 15,
  },
  infoCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingBottom: 5,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 5,
  },
  label: {
    fontSize: 16,
    color: '#666',
    flex: 1,
  },
  value: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
  },
  notesText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 22,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 10,
  },
  errorText: {
    fontSize: 18,
    color: '#f44336',
    textAlign: 'center',
    marginBottom: 20,
  },
  notFoundText: {
    fontSize: 18,
    color: '#f44336',
    textAlign: 'center',
    marginBottom: 10,
  },
  notFoundSubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  actionButtons: {
    flexDirection: 'row',
    padding: 15,
    gap: 10,
  },
  retryButton: {
    backgroundColor: '#FF9800',
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    minWidth: 120,
  },
  homeButton: {
    backgroundColor: '#2196F3',
    borderRadius: 8,
    padding: 15,
    minWidth: 120,
  },
  scanButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 8,
    padding: 15,
    flex: 1,
  },
  searchButton: {
    backgroundColor: '#2196F3',
    borderRadius: 8,
    padding: 15,
    flex: 1,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});
