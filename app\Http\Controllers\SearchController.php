<?php

namespace App\Http\Controllers;

use App\Models\Decoupe;
use App\Models\Elevage;
use App\Models\Fabrication;
use App\Models\Production;
use App\Models\Sacrifice;
use Illuminate\Http\Request;

class SearchController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    public function search(Request $request)
    {
        // Check if this is an API request (for mobile app)
        if ($request->expectsJson() || $request->is('api/*')) {
            return $this->searchApi($request);
        }

        // Perform search across multiple models for web view
        //dd($request);

        // Search in Production
        $productions = Production::where('num_lot_production', 'like', '%' . $request->search . '%')
            ->get();

        // Search in Elevage
        $elevages = Elevage::where('num_lot_elevage', 'like', '%' . $request->search . '%')
            ->get();

        // Search in Sacrifice
        $sacrifices = Sacrifice::where('num_lot_sacrifice', 'like', '%' . $request->search . '%')
            ->get();

        // Search in Decoupe
        $decoupes = Decoupe::where('num_lot_decoupe', 'like', '%' . $request->search . '%')
            ->get();

        // Search in Fabrication
        $fabrications = Fabrication::where('num_lot_fabrication', 'like', '%' . $request->search . '%')
            ->get();

        // Combine all results into a single collection
        $results = collect([$productions, $elevages, $sacrifices, $decoupes, $fabrications])
            ->collapse();

        // Return the search results to the view
        return view('search_result', compact('results'));
    }

    public function searchApi(Request $request)
    {
        // Validate the request
        $request->validate([
            'search' => 'required|string|min:1|max:50'
        ]);

        try {
            $searchTerm = $request->search;

            // Search in Production
            $productions = Production::where('num_lot_production', 'like', '%' . $searchTerm . '%')
                ->get();

            // Search in Elevage
            $elevages = Elevage::where('num_lot_elevage', 'like', '%' . $searchTerm . '%')
                ->get();

            // Search in Sacrifice
            $sacrifices = Sacrifice::where('num_lot_sacrifice', 'like', '%' . $searchTerm . '%')
                ->get();

            // Search in Decoupe
            $decoupes = Decoupe::where('num_lot_decoupe', 'like', '%' . $searchTerm . '%')
                ->get();

            // Search in Fabrication
            $fabrications = Fabrication::where('num_lot_fabrication', 'like', '%' . $searchTerm . '%')
                ->get();

            // Combine all results into a single collection
            $allResults = collect([$productions, $elevages, $sacrifices, $decoupes, $fabrications])
                ->collapse();

            if ($allResults->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'No lot found with the provided number',
                    'data' => null
                ], 404);
            }

            // For mobile app, return the first match with standardized format
            $firstResult = $allResults->first();

            // Standardize the response format
            $lotData = $this->formatLotData($firstResult);

            return response()->json([
                'success' => true,
                'message' => 'Lot found successfully',
                'data' => $lotData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while searching',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    private function formatLotData($result)
    {
        // Determine the type and format accordingly
        $type = class_basename($result);

        switch ($type) {
            case 'Production':
                return [
                    'num_lot' => $result->num_lot_production,
                    'product_name' => 'Production Lot',
                    'production_date' => $result->created_at ? $result->created_at->format('Y-m-d') : null,
                    'expiry_date' => null,
                    'batch_size' => null,
                    'status' => 'Active',
                    'location' => 'Production',
                    'quality_check' => 'Passed',
                    'notes' => 'Production lot number: ' . $result->num_lot_production,
                    'type' => 'Production'
                ];
            case 'Elevage':
                return [
                    'num_lot' => $result->num_lot_elevage,
                    'product_name' => 'Elevage Lot',
                    'production_date' => $result->created_at ? $result->created_at->format('Y-m-d') : null,
                    'expiry_date' => null,
                    'batch_size' => null,
                    'status' => 'Active',
                    'location' => 'Elevage',
                    'quality_check' => 'Passed',
                    'notes' => 'Elevage lot number: ' . $result->num_lot_elevage,
                    'type' => 'Elevage'
                ];
            case 'Sacrifice':
                return [
                    'num_lot' => $result->num_lot_sacrifice,
                    'product_name' => 'Sacrifice Lot',
                    'production_date' => $result->created_at ? $result->created_at->format('Y-m-d') : null,
                    'expiry_date' => null,
                    'batch_size' => null,
                    'status' => 'Active',
                    'location' => 'Sacrifice',
                    'quality_check' => 'Passed',
                    'notes' => 'Sacrifice lot number: ' . $result->num_lot_sacrifice,
                    'type' => 'Sacrifice'
                ];
            case 'Decoupe':
                return [
                    'num_lot' => $result->num_lot_decoupe,
                    'product_name' => 'Decoupe Lot',
                    'production_date' => $result->created_at ? $result->created_at->format('Y-m-d') : null,
                    'expiry_date' => null,
                    'batch_size' => null,
                    'status' => 'Active',
                    'location' => 'Decoupe',
                    'quality_check' => 'Passed',
                    'notes' => 'Decoupe lot number: ' . $result->num_lot_decoupe,
                    'type' => 'Decoupe'
                ];
            case 'Fabrication':
                return [
                    'num_lot' => $result->num_lot_fabrication,
                    'product_name' => 'Fabrication Lot',
                    'production_date' => $result->created_at ? $result->created_at->format('Y-m-d') : null,
                    'expiry_date' => null,
                    'batch_size' => null,
                    'status' => 'Active',
                    'location' => 'Fabrication',
                    'quality_check' => 'Passed',
                    'notes' => 'Fabrication lot number: ' . $result->num_lot_fabrication,
                    'type' => 'Fabrication'
                ];
            default:
                return [
                    'num_lot' => 'Unknown',
                    'product_name' => 'Unknown Product',
                    'production_date' => null,
                    'expiry_date' => null,
                    'batch_size' => null,
                    'status' => 'Unknown',
                    'location' => 'Unknown',
                    'quality_check' => 'Unknown',
                    'notes' => 'Unknown lot type',
                    'type' => 'Unknown'
                ];
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
