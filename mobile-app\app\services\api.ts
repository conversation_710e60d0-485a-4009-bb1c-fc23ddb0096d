import axios from 'axios';

// Configuration
const API_BASE_URL = 'http://localhost:8000'; // Your Laravel app URL
const API_TIMEOUT = 10000; // 10 seconds

// Create axios instance with default configuration
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Request interceptor for adding auth tokens if needed
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token if available
    // const token = getAuthToken();
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling common errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response) {
      // Server responded with error status
      console.error('API Error:', error.response.data);
    } else if (error.request) {
      // Request was made but no response received
      console.error('Network Error:', error.request);
    } else {
      // Something else happened
      console.error('Error:', error.message);
    }
    return Promise.reject(error);
  }
);

// Types
export interface LotData {
  num_lot: string;
  product_name?: string;
  production_date?: string;
  expiry_date?: string;
  batch_size?: number;
  status?: string;
  location?: string;
  quality_check?: string;
  notes?: string;
  type?: string;
  created_at?: string;
  updated_at?: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// API Functions
export const lotApi = {
  // Search for a lot by number using your Laravel route
  searchLot: async (numLot: string): Promise<LotData> => {
    try {
      const response = await apiClient.post<ApiResponse<LotData>>('/home_search', {
        search: numLot
      }, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        }
      });

      if (response.data.success && response.data.data) {
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Lot not found');
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 404) {
          throw new Error('Lot number not found');
        } else if (error.response?.status === 422) {
          throw new Error('Invalid lot number format');
        } else if (error.response?.status === 500) {
          throw new Error('Server error. Please try again later.');
        } else if (error.code === 'ECONNABORTED') {
          throw new Error('Request timeout. Please check your connection.');
        } else if (!error.response) {
          throw new Error('Network error. Please check your connection.');
        }
      }
      throw error;
    }
  },

  // Get all lots (for future use)
  getAllLots: async (): Promise<LotData[]> => {
    try {
      const response = await apiClient.get<ApiResponse<LotData[]>>('/lots');
      
      if (response.data.success && response.data.data) {
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to fetch lots');
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 500) {
          throw new Error('Server error. Please try again later.');
        } else if (error.code === 'ECONNABORTED') {
          throw new Error('Request timeout. Please check your connection.');
        } else if (!error.response) {
          throw new Error('Network error. Please check your connection.');
        }
      }
      throw error;
    }
  },

  // Create a new lot (for future use)
  createLot: async (lotData: Omit<LotData, 'created_at' | 'updated_at'>): Promise<LotData> => {
    try {
      const response = await apiClient.post<ApiResponse<LotData>>('/lots', lotData);
      
      if (response.data.success && response.data.data) {
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to create lot');
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 422) {
          throw new Error('Invalid data provided');
        } else if (error.response?.status === 500) {
          throw new Error('Server error. Please try again later.');
        } else if (error.code === 'ECONNABORTED') {
          throw new Error('Request timeout. Please check your connection.');
        } else if (!error.response) {
          throw new Error('Network error. Please check your connection.');
        }
      }
      throw error;
    }
  },
};

// Mock API for development/testing
export const mockLotApi = {
  searchLot: async (numLot: string): Promise<LotData> => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000));
    
    // Simulate different scenarios based on lot number
    if (numLot.toLowerCase().includes('error')) {
      throw new Error('Simulated server error');
    }
    
    if (numLot.toLowerCase().includes('notfound')) {
      throw new Error('Lot number not found');
    }
    
    if (numLot.toLowerCase().includes('timeout')) {
      throw new Error('Request timeout. Please check your connection.');
    }
    
    // Return mock data
    const mockData: LotData = {
      num_lot: numLot.toUpperCase(),
      product_name: `Product for ${numLot}`,
      production_date: '2024-01-15',
      expiry_date: '2024-12-15',
      batch_size: Math.floor(Math.random() * 5000) + 500,
      status: Math.random() > 0.2 ? 'Active' : 'Expired',
      location: ['Warehouse A', 'Warehouse B', 'Storage C'][Math.floor(Math.random() * 3)],
      quality_check: Math.random() > 0.1 ? 'Passed' : 'Failed',
      notes: 'All quality checks completed successfully. Product meets all specifications.',
      created_at: '2024-01-15T10:30:00Z',
      updated_at: '2024-01-20T14:45:00Z',
    };
    
    return mockData;
  },
};

// Export the API to use (switch between real and mock)
export const api = __DEV__ ? mockLotApi : lotApi;

export default api;
