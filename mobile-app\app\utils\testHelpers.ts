// Test helpers and utilities for the app

export const testLotNumbers = {
  valid: [
    'LOT001',
    'BATCH-2024-001',
    'TEST_LOT_123',
    'ABC123',
    'XYZ-789',
  ],
  invalid: [
    '', // Empty
    'A', // Too short
    'A'.repeat(51), // Too long
    'LOT@001', // Invalid characters
    'LOT 001', // Spaces not allowed
  ],
  special: [
    'ERROR', // Will trigger error in mock API
    'NOTFOUND', // Will trigger not found in mock API
    'TIMEOUT', // Will trigger timeout in mock API
  ],
};

export const mockLotData = {
  'LOT001': {
    num_lot: 'LOT001',
    product_name: 'Premium Widget A',
    production_date: '2024-01-15',
    expiry_date: '2024-12-15',
    batch_size: 1000,
    status: 'Active',
    location: 'Warehouse A',
    quality_check: 'Passed',
    notes: 'All quality checks completed successfully.',
  },
  'BATCH-2024-001': {
    num_lot: 'BATCH-2024-001',
    product_name: 'Standard Component B',
    production_date: '2024-02-01',
    expiry_date: '2025-02-01',
    batch_size: 2500,
    status: 'Active',
    location: 'Warehouse B',
    quality_check: 'Passed',
    notes: 'High-quality batch with extended shelf life.',
  },
  'TEST_LOT_123': {
    num_lot: 'TEST_LOT_123',
    product_name: 'Test Product C',
    production_date: '2024-03-10',
    expiry_date: '2024-09-10',
    batch_size: 500,
    status: 'Expired',
    location: 'Storage C',
    quality_check: 'Failed',
    notes: 'Quality check failed. Do not use.',
  },
};

export const testScenarios = [
  {
    name: 'Valid lot number search',
    input: 'LOT001',
    expectedResult: 'success',
    description: 'Should return lot data for valid lot number',
  },
  {
    name: 'Invalid lot number format',
    input: 'LOT@001',
    expectedResult: 'validation_error',
    description: 'Should show validation error for invalid characters',
  },
  {
    name: 'Empty lot number',
    input: '',
    expectedResult: 'validation_error',
    description: 'Should show validation error for empty input',
  },
  {
    name: 'Lot number not found',
    input: 'NOTFOUND',
    expectedResult: 'not_found',
    description: 'Should show not found message',
  },
  {
    name: 'Server error simulation',
    input: 'ERROR',
    expectedResult: 'server_error',
    description: 'Should handle server errors gracefully',
  },
  {
    name: 'Network timeout simulation',
    input: 'TIMEOUT',
    expectedResult: 'timeout_error',
    description: 'Should handle network timeouts',
  },
];

export const validateTestResult = (scenario: typeof testScenarios[0], actualResult: string): boolean => {
  return scenario.expectedResult === actualResult;
};

export const runBasicValidationTests = () => {
  console.log('Running basic validation tests...');
  
  const results = testScenarios.map(scenario => {
    console.log(`Testing: ${scenario.name}`);
    console.log(`Input: "${scenario.input}"`);
    console.log(`Expected: ${scenario.expectedResult}`);
    console.log(`Description: ${scenario.description}`);
    console.log('---');
    
    return {
      scenario: scenario.name,
      passed: false, // This would be set based on actual test execution
    };
  });
  
  return results;
};

export default {
  testLotNumbers,
  mockLotData,
  testScenarios,
  validateTestResult,
  runBasicValidationTests,
};
