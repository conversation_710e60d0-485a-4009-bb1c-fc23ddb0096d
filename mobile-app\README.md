# Lot Scanner Mobile App

A React Native mobile application for searching and scanning lot numbers, built with TypeScript and Expo.

## Features

- 🔍 **Search by Lot Number**: Enter lot numbers manually to search for product information
- 📱 **Barcode Scanning**: Use camera to scan barcodes and QR codes for lot numbers
- 📊 **Detailed Results**: View comprehensive lot information including production dates, quality checks, and location
- 🎨 **Modern UI**: Clean, intuitive interface with proper loading states and error handling
- 🔄 **Real-time API Integration**: Connects to Laravel backend for live data retrieval

## Tech Stack

- **React Native** with **Expo**
- **TypeScript** for type safety
- **React Navigation** for screen navigation
- **Expo Barcode Scanner** for scanning functionality
- **Axios** for API communication
- **Laravel Backend** integration

## Installation

1. **Prerequisites**
   ```bash
   npm install -g expo-cli
   ```

2. **Install Dependencies**
   ```bash
   cd mobile-app
   npm install
   ```

3. **Start Development Server**
   ```bash
   npm start
   ```

4. **Run on Device/Simulator**
   - Install Expo Go app on your mobile device
   - Scan the QR code from the terminal
   - Or press `a` for Android emulator, `i` for iOS simulator

## Configuration

Update the API base URL in `app/services/api.ts`:

```typescript
const API_BASE_URL = 'http://your-laravel-app-url.com';
```

For local development, use your computer's IP address:
```typescript
const API_BASE_URL = 'http://*************:8000'; // Replace with your IP
```

## API Integration

The app connects to your Laravel backend using the `/home_search` endpoint:

```typescript
// Request format
{
  "search": "LOT123"
}

// Response format
{
  "success": true,
  "message": "Lot found successfully",
  "data": {
    "num_lot": "LOT123",
    "product_name": "Product Name",
    "type": "Production",
    "production_date": "2024-01-15",
    "status": "Active",
    "location": "Warehouse A",
    "quality_check": "Passed",
    "notes": "Additional information"
  }
}
```

## Testing

### Manual Testing Scenarios

1. **Valid Lot Search**
   - Enter a valid lot number
   - Verify correct data is displayed
   - Check all information fields

2. **Invalid Lot Search**
   - Enter non-existent lot number
   - Verify "not found" message appears
   - Test retry functionality

3. **Barcode Scanning**
   - Test camera permissions
   - Scan various barcode types
   - Verify scanned data navigates to results

4. **Error Handling**
   - Test with network disconnected
   - Verify error messages display
   - Test retry functionality

5. **Navigation Flow**
   - Test all screen transitions
   - Verify back navigation works
   - Test "New Search" and "Scan Another" buttons

### Test Lot Numbers

For testing purposes, you can use these lot numbers:
- `LOT001` - Valid production lot
- `BATCH-2024-001` - Valid batch
- `TEST_LOT_123` - Test lot
- `NOTFOUND` - Will return not found
- `ERROR` - Will simulate server error

## Project Structure

```
mobile-app/
├── App.tsx                 # Main app component with navigation
├── app/
│   ├── components/         # Reusable UI components
│   │   ├── LoadingSpinner.tsx
│   │   └── ErrorMessage.tsx
│   ├── screens/           # App screens
│   │   ├── HomeScreen.tsx
│   │   ├── ScannerScreen.tsx
│   │   └── ResultsScreen.tsx
│   ├── services/          # API and external services
│   │   └── api.ts
│   ├── styles/            # Styling constants
│   │   ├── colors.ts
│   │   └── typography.ts
│   └── config/            # App configuration
│       └── config.ts
├── assets/                # Images and static assets
└── package.json
```

## Features in Detail

### Home Screen
- Text input for manual lot number entry
- Input validation (length, format)
- Search button with loading state
- Scan barcode button

### Scanner Screen
- Camera permission handling
- Barcode/QR code scanning
- Visual scanning frame
- Auto-navigation to results

### Results Screen
- Comprehensive lot information display
- Organized information cards
- Loading states during API calls
- Error handling with retry options
- Action buttons for new search/scan

## Troubleshooting

### Common Issues

1. **Camera not working**
   - Check device permissions
   - Ensure physical device (camera doesn't work in simulator)

2. **API connection issues**
   - Verify Laravel server is running
   - Check network connectivity
   - Confirm API URL is correct
   - For local development, use computer's IP address

3. **Build errors**
   - Clear node_modules: `rm -rf node_modules && npm install`
   - Clear Expo cache: `expo start -c`

### Development Tips

- Use `__DEV__` flag to switch between mock and real API
- Check console logs for API errors
- Test on physical device for camera functionality
- Use Expo DevTools for debugging

## Contributing

1. Follow TypeScript best practices
2. Use the established component structure
3. Add proper error handling
4. Test on both iOS and Android
5. Update documentation for new features

## License

This project is part of the Archive system and follows the same licensing terms.
